def main():
    from deepsearcher.configuration import Configuration, init_config
    from deepsearcher.online_query import query

    config = Configuration()

    # Customize your config here,
    # more configuration see the Configuration Details section below.
    # config.set_provider_config("llm", "OpenAI", {"model": "o1-mini"})
    config.set_provider_config("llm", "DeepSeek", {"model": "deepseek-reasoner"})

    # 使用MilvusEmbedding，现在应该有pymilvus-model支持了
    config.set_provider_config(
        "embedding",
        "MilvusEmbedding",
        {
            "model": "BAAI/bge-base-en-v1.5",  # 可选内置模型（如bge系列、jina-embeddings-v3等）
        },
    )

    # 配置文件加载器 - 使用 PDF 加载器（默认）
    config.set_provider_config(
        "file_loader",
        "PDFLoader",
        {},
    )

    # 配置向量数据库连接 - 使用本地 Milvus Lite（用于测试）
    config.set_provider_config(
        "vector_db",
        "Milvus",
        {
            "default_collection": "deepsearcher",
            "uri": "./milvus.db",  # 本地文件数据库
            "token": "root:Milvus",  # 本地默认token
            "db": "default"
        },
    )

    # 如果你想使用远程 Milvus 服务，请确保服务器地址和端口正确，然后取消注释下面的配置：
    # config.set_provider_config(
    #     "vector_db",
    #     "Milvus",
    #     {
    #         "default_collection": "deepsearcher",
    #         "uri": "http://opensearch-cn-shanghai.aliyuncs.com:19530",  # 确保端口号正确
    #         "token": "OS-12909pq47rllo9ct",  # 从阿里云控制台获取的免费API密钥
    #         "db": "default"
    #     },
    # )
    init_config(config=config)

    # Load your local data
    from deepsearcher.offline_loading import load_from_local_files

    load_from_local_files(paths_or_directory="./tmpki79mvp__origin.pdf")
    # load_from_local_files(paths_or_directory="./tmpki79mvp__content_list.json")

    # (Optional) Load from web crawling (`FIRECRAWL_API_KEY` env variable required)
    # from deepsearcher.offline_loading import load_from_website
    # load_from_website(urls=website_url)

    # Query
    result = query(
        "You need to extract specific information from the PDF including basic information (project name, bidding number, tenderer information, agency), commercial requirements (bidding time, location, bidder qualifications, bid security, performance security, payment terms, price composition, registered capital, qualification grade, performance requirements), technical requirements (technical specifications, performance indicators, quality standards, acceptance criteria, personnel requirements, delivery requirements, maintenance requirements, certification materials), bid rejection items (bid rejection conditions), and scoring items (scoring standards)."
    )  # Your question here
    print(result)


if __name__ == "__main__":
    main()
